# affiliates-aws-authorizer-lambda

This project provides an AWS Lambda authorizer for XM affiliates, built with Java and packaged as a native executable using GraalVM. It is designed to run efficiently in AWS Lambda environments, providing custom authorization logic for API Gateway requests.

## Features
- Custom Lambda authorizer for API Gateway
- Native image build with GraalVM for fast cold starts
- Docker-based build and local test workflow

## Prerequisites
- Docker
- Java 17+
- GraalVM (via provided Docker image)
- AWS CLI (optional, for deployment)

## Build

``` bash
    docker run --rm --platform linux/amd64 \
    -m 8g --memory-swap 8g \
    -v $(pwd):/project \
    -v $HOME/.gradle:/root/.gradle \
    -w /project \
    registry.xm.com/xmdevs/graalvm-xm:22.1.0 \
    -c "chmod +x gradlew && ./gradlew clean nativeCompile"
```

native executable binary will be generated under `build/native/nativeCompile/native`

## Run and test locally

``` bash
    docker run --platform linux/amd64 -p 9000:8080 \
    -v ./build/native/nativeCompile/native:/var/task/native \
    -v ./src/main/resources/bootstrap:/var/runtime/bootstrap \
    -e _HANDLER=com.xm.affiliates.lambda.Authorizer::handleRequest \
    public.ecr.aws/lambda/provided:al2023 \
    com.xm.affiliates.lambda.Authorizer::handleRequest
```

Invoke the Lambda authorizer with a sample event:

``` bash
    curl \
    -X POST http://localhost:9000/2015-03-31/functions/function/invocations \
    -H "Content-Type: application/json" \
    -d '{
       "routeArn": "arn:aws:execute-api:us-east-1:123456789012:my-api-id/prod/POST/request", 
       "cookies": [
           "session=12345"
       ]
   }'
```

## Project Structure
- `src/main/java/com/xm/affiliates/Authorizer.java`: Main authorizer logic
- `src/test/java/com/xm/affiliates/AuthorizerTest.java`: Unit tests
- `build.gradle`: Build configuration
- `build/native/nativeCompile/native`: Native executable output

## License
[Specify your license here]

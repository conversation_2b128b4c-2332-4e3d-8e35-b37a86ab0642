version: '3.7'

services:
  redis-node-1:
    image: redis:6.0
    hostname: redis-node-1
    container_name: redis-node-1
    volumes:
      - ./data/node1:/data
    ports:
      - "7001:6379"
    networks:
      - redis-cluster
    command: ["redis-server", "--cluster-enabled", "yes", "--cluster-config-file", "/data/node1.conf", "--cluster-node-timeout", "5000"]

  redis-node-2:
    image: redis:6.0
    hostname: redis-node-2
    container_name: redis-node-2
    volumes:
      - ./data/node2:/data
    ports:
      - "7002:6379"
    networks:
      - redis-cluster
    command: ["redis-server", "--cluster-enabled", "yes", "--cluster-config-file", "/data/node2.conf", "--cluster-node-timeout", "5000"]

  redis-node-3:
    image: redis:6.0
    hostname: redis-node-3
    container_name: redis-node-3
    volumes:
      - ./data/node3:/data
    ports:
      - "7003:6379"
    networks:
      - redis-cluster
    command: ["redis-server", "--cluster-enabled", "yes", "--cluster-config-file", "/data/node3.conf", "--cluster-node-timeout", "5000"]

  redisinsight:
    image: redislabs/redisinsight:latest
    hostname: redisinsight
    container_name: redisinsight
    restart: unless-stopped
    ports:
      - "5540:5540"
    networks:
      - redis-cluster

networks:
  redis-cluster:
    driver: bridge
version: '3.7'

services:
  redis-node-1:
    image: redis:6.0
    hostname: redis-node-1
    container_name: redis-node-1
    volumes:
      - ./data/node1:/data
    ports:
      - "7001:6379"
    networks:
      - redis-cluster
    command: ["redis-server", "--cluster-enabled", "yes", "--cluster-config-file", "/data/node1.conf", "--cluster-node-timeout", "5000", "--bind", "0.0.0.0", "--protected-mode", "no"]

  redis-node-2:
    image: redis:6.0
    hostname: redis-node-2
    container_name: redis-node-2
    volumes:
      - ./data/node2:/data
    ports:
      - "7002:6379"
    networks:
      - redis-cluster
    command: ["redis-server", "--cluster-enabled", "yes", "--cluster-config-file", "/data/node2.conf", "--cluster-node-timeout", "5000"]

  redis-node-3:
    image: redis:6.0
    hostname: redis-node-3
    container_name: redis-node-3
    volumes:
      - ./data/node3:/data
    ports:
      - "7003:6379"
    networks:
      - redis-cluster
    command: ["redis-server", "--cluster-enabled", "yes", "--cluster-config-file", "/data/node3.conf", "--cluster-node-timeout", "5000"]

  redis-cluster-init:
    image: redis:6.0
    container_name: redis-cluster-init
    depends_on:
      - redis-node-1
      - redis-node-2
      - redis-node-3
    networks:
      - redis-cluster
    command: >
      sh -c "
        sleep 10 &&
        redis-cli --cluster create
        redis-node-1:6379
        redis-node-2:6379
        redis-node-3:6379
        --cluster-replicas 0
        --cluster-yes &&
        echo 'Redis cluster initialized successfully'
      "
    restart: "no"

  redisinsight:
    image: redislabs/redisinsight:latest
    hostname: redisinsight
    container_name: redisinsight
    restart: unless-stopped
    ports:
      - "5540:5540"
    networks:
      - redis-cluster

networks:
  redis-cluster:
    driver: bridge
#!/usr/bin/groovy
@Library('xm-pipeline-library@0.2.48') _

import com.xm.gitflow.Constants
import com.xm.gitflow.version.FileProjectVersionRepository
import com.xm.gitflow.version.ProjectVersion

def shouldDeploy = env.gitlabActionType != 'MERGE' && env.gitlabActionType != 'NOTE'
def totalStatus = 'Total status'
def nexusCredentialsId = 'nexusArtifactDeployerUser'
def deployJob = params.deployJob
def deployParameters = [
        string(name: 'environment', value: params.environment ?: 'dev'),
        string(name: 'environment', value: params.source)

]

pipeline {
    options {
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '50', artifactNumToKeepStr: '50'))
        gitLabConnection('gitlab')
        gitlabBuilds(builds: [totalStatus])
        ansiColor('xmAnsibleColors')
    }

    agent {
        node {
            label 'docker_build'
        }
    }

    stages {
        stage('Preparation') {
            steps {
                checkoutMerged('*****************:piktiv/affiliates-aws-authorizer-lambda.git', params.source)
            }
            post {
                success {
                    updateGitlabCommitStatus name: 'Preparation', state: 'success'
                }
                failure {
                    updateGitlabCommitStatus name: 'Preparation', state: 'failed'
                }
            }
        }

        stage('Build') {
            steps {
                script {

                    def jenkinsGID = sh(returnStdout: true, script: 'id -g ${USER}').trim()

                    docker.withRegistry('http://registry.xm.com') {
                        sh "docker run -di -v \"" + pwd() + '":/project:z' +
                                " -v ~/.gradle:/root/.gradle " +
                                " --entrypoint='' -u ${jenkinsGID}:${jenkinsGID}" +
                                " --memory=8g --name graal-vm registry.xm.com/xmdevs/graalvm-xm:22.1.0 cat"
                        sh "docker logs graal-vm"
                        sh "docker exec graal-vm ./gradlew clean nativeCompile createDistributionZip"
                    }
                }
            }
            post {
                always {
                    sh "docker rm -f graal-vm"
                }
                success {
                    updateGitlabCommitStatus name: 'Build', state: 'success'
                }
                failure {
                    updateGitlabCommitStatus name: 'Build', state: 'failed'
                }
            }
        }

        stage('Publishing to Nexus') {
            when {
                beforeAgent true
                expression { shouldDeploy }
            }
            steps {
                script {
                    Constants.updateConstants(params)

                    ProjectVersion version = new FileProjectVersionRepository(this).get()
                    if (version.isSnapshot()) {
                        version = version.postfixNumberAsDateTime()
                    }
                    def artifactVersion = version.getVersionString()
                    echo "Artifact version ${artifactVersion}"

                    currentBuild.description = currentBuild.description ? currentBuild.description + "\n" : ""
                    currentBuild.description += "Artifact version: ${artifactVersion}\n"

                    withCredentials([usernamePassword(credentialsId: nexusCredentialsId, usernameVariable: 'nexusUsername', passwordVariable: 'nexusPassword')]) {
                        sh 'chmod +x gradlew'
                        sh "./gradlew publish -Pversion=${artifactVersion} -PxmReleasesUsername=\${nexusUsername} -PxmReleasesPassword=\${nexusPassword}"
                    }
                    deployParameters.add(string(name: "artifactVersion", value: artifactVersion))

                    println "Published artifact version ${artifactVersion}."
                }
            }
            post {
                success {
                    updateGitlabCommitStatus name: 'Publishing to Nexus', state: 'success'
                }
                failure {
                    updateGitlabCommitStatus name: 'Publishing to Nexus', state: 'failed'
                }
            }
        }

        stage('Trigger deployment') {
            when {
                beforeAgent true
                expression { shouldDeploy }
            }
            steps {
                script {
                    build job: deployJob, parameters: deployParameters, wait: false
                }
            }
            post {
                success {
                    updateGitlabCommitStatus name: 'Trigger deployment', state: 'success'
                }
                failure {
                    updateGitlabCommitStatus name: 'Trigger deployment', state: 'failed'
                }
            }
        }

    }

    post {
        always {
            archiveArtifacts artifacts: 'build/**', allowEmptyArchive: true
        }
        cleanup {
            cleanWs()
        }
        success {
            updateGitlabCommitStatus name: totalStatus, state: 'success'
        }
        failure {
            updateGitlabCommitStatus name: totalStatus, state: 'failed'
        }
    }
}

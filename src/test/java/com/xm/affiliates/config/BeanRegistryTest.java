package com.xm.affiliates.config;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xm.affiliates.exception.AuthorizerException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class BeanRegistryTest {

    private BeanRegistry beanRegistry;

    @BeforeEach
    void setUp() {
        beanRegistry = new BeanRegistry();
    }

    @Test
    void whenRegisteringBean_thenRetrieveSuccessfully() {
        String testBean = "Test String Bean";

        beanRegistry.register(String.class, testBean);
        String retrievedBean = beanRegistry.getBean(String.class);

        assertEquals(testBean, retrievedBean);
    }

    @Test
    void whenRegisteringMultipleBeansOfDifferentTypes_thenRetrieveAllSuccessfully() {
        String stringBean = "String Bean";
        Integer integerBean = 42;

        beanRegistry.register(String.class, stringBean);
        beanRegistry.register(Integer.class, integerBean);

        assertEquals(stringBean, beanRegistry.getBean(String.class));
        assertEquals(integerBean, beanRegistry.getBean(Integer.class));
    }

    @Test
    void whenRegisteringSameTypeTwice_thenOverrideExistingBean() {
        String firstBean = "First Bean";
        String secondBean = "Second Bean";

        beanRegistry.register(String.class, firstBean);
        beanRegistry.register(String.class, secondBean);
        String retrievedBean = beanRegistry.getBean(String.class);

        assertEquals(secondBean, retrievedBean);
        assertNotEquals(firstBean, retrievedBean);
    }

    @Test
    void whenRetrievingNonExistentBean_thenThrowAuthorizerException() {
        AuthorizerException exception = assertThrows(AuthorizerException.class,
            () -> beanRegistry.getBean(String.class));

        assertEquals("No bean registered for type: java.lang.String", exception.getMessage());
    }

    @Test
    void whenBeanExists_thenReturnTrue() {
        String testBean = "Test Bean";
        beanRegistry.register(String.class, testBean);
        boolean result = beanRegistry.hasBean(String.class);

        assertTrue(result);
    }

    @Test
    void whenBeanDoesNotExist_thenReturnFalse() {
        boolean result = beanRegistry.hasBean(String.class);
        assertFalse(result);
    }

    @Test
    void whenRegisteringNullBean_thenThrowAuthorizerException() {
        AuthorizerException exception = assertThrows(AuthorizerException.class,
            () -> beanRegistry.register(String.class, null));

        assertEquals("Cannot register null bean for type: java.lang.String", exception.getMessage());
    }
}
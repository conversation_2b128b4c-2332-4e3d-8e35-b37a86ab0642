package com.xm.affiliates.cache;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xm.affiliates.dto.UserSession;
import com.xm.affiliates.exception.AuthorizerException;
import com.xm.affiliates.repository.UserSessionRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import redis.clients.jedis.JedisCluster;

import static com.xm.affiliates.utils.TestHelper.buildSessionAttributes;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserSessionRepositoryTest {

    @InjectMocks
    UserSessionRepository userSessionRepository;
    @Mock
    JedisCluster redisClient;
    @Mock
    ObjectMapper objectMapper;

    static final String SESSION_ATTRIBUTES_VALUE = """
            {
              "created": 1719916800,
              "userId": 12345,
              "isManager": true,
              "csrfToken": "abc123csrf",
              "canSeeAllBrands": false,
              "azureADSessionId": "aad-session-67890",
              "isDev": true,
              "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
              "refreshToken": "def456refresh"
            }""";

    @Test
    void whenSessionExists_thenReturnAttributes() throws Exception {
        String sessionId = "abc123";
        UserSession attributes = buildSessionAttributes();

        when(redisClient.get(anyString())).thenReturn(SESSION_ATTRIBUTES_VALUE);
        when(objectMapper.readValue(SESSION_ATTRIBUTES_VALUE, UserSession.class)).thenReturn(attributes);

        Optional<UserSession> result = userSessionRepository.findUserSessionBySessionId(sessionId);

        assertTrue(result.isPresent());
        assertEquals(attributes, result.get());
    }

    @Test
    void whenSessionNotFound_thenReturnEmpty() {
        String sessionId = "notfound";
        when(redisClient.get(anyString())).thenReturn(null);

        Optional<UserSession> result = userSessionRepository.findUserSessionBySessionId(sessionId);

        assertTrue(result.isEmpty());
    }

    @Test
    void whenDeserializationFails_thenThrowException() throws Exception {
        String sessionId = "badjson";
        String json = "bad json";

        when(redisClient.get(anyString())).thenReturn(json);
        when(objectMapper.readValue(json, UserSession.class)).thenThrow(new JsonProcessingException("fail"){});

        AuthorizerException ex = assertThrows(AuthorizerException.class, () ->
                userSessionRepository.findUserSessionBySessionId(sessionId)
        );

        assertTrue(ex.getMessage().contains("Could not deserialize"));
    }
}
package com.xm.affiliates.utils;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2CustomAuthorizerEvent;
import com.amazonaws.services.lambda.runtime.events.IamPolicyResponse;
import com.xm.affiliates.lambda.Authorizer;
import java.util.List;

public class AuthorizerRunner {

    public static void main(String[] args) {
        APIGatewayV2CustomAuthorizerEvent request = new APIGatewayV2CustomAuthorizerEvent();
        request.setCookies(List.of("session=abc123"));
        request.setRouteArn("arn:aws:execute-api:us-east-1:123456789012:example/prod/GET/resource");

        Context context = new TestContext();

        Authorizer handler = new Authorizer();
        IamPolicyResponse response = handler.handleRequest(request, context);

        System.out.println("Auth Response: " + response);
    }
}
package com.xm.affiliates.utils;

import com.amazonaws.services.lambda.runtime.ClientContext;
import com.amazonaws.services.lambda.runtime.CognitoIdentity;
import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.LambdaLogger;

public class TestContext implements Context {

    public String getAwsRequestId() {
        return "test-request-id";
    }

    public String getLogGroupName() {
        return "test-log-group";
    }

    public String getLogStreamName() {
        return "test-log-stream";
    }

    public String getFunctionName() {
        return "test-function";
    }

    public String getFunctionVersion() {
        return "1.0";
    }

    public String getInvokedFunctionArn() {
        return "arn:aws:lambda:test-function";
    }

    public CognitoIdentity getIdentity() {
        return null;
    }

    public ClientContext getClientContext() {
        return null;
    }

    public int getRemainingTimeInMillis() {
        return 300000;
    }

    public int getMemoryLimitInMB() {
        return 512;
    }

    public LambdaLogger getLogger() {
        return new TestLambdaLogger();
    }
}
package com.xm.affiliates.utils;

import com.xm.affiliates.dto.UserSession;

public final class TestHelper {

    public static UserSession buildSessionAttributes() {
        return UserSession.builder()
            .created(1719916800L)
            .userId(12345L)
            .isManager(true)
            .csrfToken("abc123csrf")
            .canSeeAllBrands(false)
            .azureADSessionId("aad-session-67890")
            .isDev(true)
            .accessToken("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9")
            .refreshToken("def456refresh")
            .build();
    }
}

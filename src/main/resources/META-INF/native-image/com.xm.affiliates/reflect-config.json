[{"name": "com.xm.affiliates.lambda.Authorizer", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredClasses": true, "allPublicClasses": true}, {"name": "org.apache.commons.pool2.impl.DefaultEvictionPolicy", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "org.apache.commons.pool2.impl.EvictionPolicy", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "org.apache.commons.pool2.impl.BaseGenericObjectPool", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "org.apache.commons.pool2.impl.GenericObjectPool", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}]
[{"name": "com.amazonaws.services.lambda.runtime.events.APIGatewayV2CustomAuthorizerEvent", "allDeclaredFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "com.amazonaws.services.lambda.runtime.events.APIGatewayV2CustomAuthorizerEvent$RequestContext", "allDeclaredFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "com.amazonaws.services.lambda.runtime.events.IamPolicyResponse", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredClasses": true, "allPublicClasses": true}, {"name": "com.amazonaws.services.lambda.runtime.events.IamPolicyResponse$PolicyDocument", "allDeclaredFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "com.amazonaws.services.lambda.runtime.events.IamPolicyResponse$Statement", "allDeclaredFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "com.amazonaws.services.lambda.runtime.events.IamPolicyResponse$PolicyDocument$PolicyDocumentBuilder", "allDeclaredFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "com.amazonaws.services.lambda.runtime.events.IamPolicyResponse$Statement$StatementBuilder", "allDeclaredFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true}]
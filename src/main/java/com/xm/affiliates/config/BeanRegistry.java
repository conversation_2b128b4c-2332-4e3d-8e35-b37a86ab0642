package com.xm.affiliates.config;

import com.xm.affiliates.exception.AuthorizerException;
import java.util.HashMap;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PACKAGE)
public final class BeanRegistry {

    private final Map<Class<?>, Object> beans = new HashMap<>();

    public <T> void register(Class<T> type, T instance) {
        if (instance == null) {
            throw new AuthorizerException("Cannot register null bean for type: " + type.getName());
        }
        beans.put(type, instance);
    }

    @SuppressWarnings("unchecked")
    public <T> T getBean(Class<T> type) {
        T bean = (T) beans.get(type);
        if (bean == null) {
            throw new AuthorizerException("No bean registered for type: " + type.getName());
        }
        return bean;
    }

    public boolean hasBean(Class<?> type) {
        return beans.containsKey(type);
    }
}

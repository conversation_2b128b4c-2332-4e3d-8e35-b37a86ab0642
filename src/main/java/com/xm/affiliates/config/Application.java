package com.xm.affiliates.config;

import static com.xm.affiliates.config.ConfigLoader.REDIS_SERVERS;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xm.affiliates.repository.UserSessionRepository;
import java.io.IOException;
import java.net.InetAddress;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import redis.clients.jedis.ConnectionPoolConfig;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public final class Application {

    private static final BeanRegistry beanRegistry = new BeanRegistry();

    public static BeanRegistry getBeanRegistry() {
        initializeBeans();
        return beanRegistry;
    }

    private static void initializeBeans() {
        final ConfigLoader configLoader = new ConfigLoader();
        beanRegistry.register(ConfigLoader.class, configLoader);

        final ObjectMapper objectMapper = new ObjectMapper();
        beanRegistry.register(ObjectMapper.class, objectMapper);

        final JedisCluster redisClient = redisClient();
        beanRegistry.register(JedisCluster.class, redisClient);

        final UserSessionRepository userSessionRepository = new UserSessionRepository(redisClient, objectMapper);
        beanRegistry.register(UserSessionRepository.class, userSessionRepository);
    }

    private static JedisCluster redisClient() {
        final String redisServers = System.getenv(REDIS_SERVERS);
        System.out.println("Redis servers: " + redisServers);

        Arrays.stream(redisServers.split(",")).map(s -> s.split(":")[0]).forEach(Application::checkReachable);

        final Set<HostAndPort> jedisClusterNodes = Arrays
            .stream(redisServers.split(","))
            .map(HostAndPort::from).collect(Collectors.toSet());
        final ConnectionPoolConfig jedisPoolConfig = new ConnectionPoolConfig();
        jedisPoolConfig.setMaxTotal(10);
        jedisPoolConfig.setMaxIdle(5);
        jedisPoolConfig.setMinIdle(1);
        System.out.println("Jedis cluster nodes: " + jedisClusterNodes);
        return new JedisCluster(jedisClusterNodes, jedisPoolConfig);
    }

    @SneakyThrows
    private static void checkReachable(final String hostname) {
        InetAddress inet = InetAddress.getByName(hostname);
        boolean reachable = inet.isReachable(5000); // timeout in ms
        System.out.println(hostname + " is reachable: " + reachable);
    }
}

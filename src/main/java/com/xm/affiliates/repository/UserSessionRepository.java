package com.xm.affiliates.repository;

import static com.xm.affiliates.config.AppConstants.CACHE_PREFIX;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xm.affiliates.dto.UserSession;
import com.xm.affiliates.exception.AuthorizerException;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import redis.clients.jedis.JedisCluster;

@RequiredArgsConstructor
public final class UserSessionRepository {

    private final JedisCluster redisClient;
    private final ObjectMapper objectMapper;

    public Optional<UserSession> findUserSessionBySessionId(final String sessionId) {
        return Optional
            .ofNullable(redisClient.get(CACHE_PREFIX + sessionId))
            .map(json -> readJsonValue(json, objectMapper));
    }

    private UserSession readJsonValue(final String json, final ObjectMapper objectMapper) {
        try {
            return objectMapper.readValue(json, UserSession.class);
        } catch (JsonProcessingException e) {
            throw new AuthorizerException("Could not deserialize json", e);
        }
    }
}

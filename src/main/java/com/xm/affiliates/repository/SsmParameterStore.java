package com.xm.affiliates.repository;

import com.amazonaws.services.simplesystemsmanagement.AWSSimpleSystemsManagement;
import com.amazonaws.services.simplesystemsmanagement.AWSSimpleSystemsManagementClientBuilder;
import com.amazonaws.services.simplesystemsmanagement.model.GetParameterRequest;
import com.amazonaws.services.simplesystemsmanagement.model.GetParameterResult;

public final class SsmParameterStore {

    private final AWSSimpleSystemsManagement ssmClient;

    public SsmParameterStore() {
        this.ssmClient = AWSSimpleSystemsManagementClientBuilder.defaultClient();
    }

    public String getParameter(String name, boolean withDecryption) {
        final GetParameterRequest request = new GetParameterRequest()
            .withName(name)
            .withWithDecryption(withDecryption);

        final GetParameterResult result = ssmClient.getParameter(request);
        return result.getParameter().getValue();
    }
}
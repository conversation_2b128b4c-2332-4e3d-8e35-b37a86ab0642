package com.xm.affiliates.lambda;

import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponse.ALLOW;
import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponse.DENY;
import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponse.PolicyDocument;
import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponse.Statement;
import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponse.builder;
import static com.xm.affiliates.config.AppConstants.ANONYMOUS;
import static com.xm.affiliates.config.AppConstants.SESSION;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.LambdaLogger;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayV2CustomAuthorizerEvent;
import com.amazonaws.services.lambda.runtime.events.IamPolicyResponse;
import com.xm.affiliates.repository.UserSessionRepository;
import com.xm.affiliates.config.Application;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public class Authorizer implements RequestHandler<APIGatewayV2CustomAuthorizerEvent, IamPolicyResponse> {

    private final UserSessionRepository userSessionRepository;

    public Authorizer() {
        userSessionRepository = Application.getBeanRegistry().getBean(UserSessionRepository.class);
    }

    @Override
    public IamPolicyResponse handleRequest(APIGatewayV2CustomAuthorizerEvent input, Context context) {
        final LambdaLogger logger = context.getLogger();

        final List<String> cookies = input.getCookies();
        final String routeArn = input.getRouteArn();

        logger.log("Cookies: " + cookies);
        logger.log("Route ARN: " + routeArn);

        return Optional
            .ofNullable(collectCookiesByName(cookies).get(SESSION))
            .flatMap(userSessionRepository::findUserSessionBySessionId)
            .map(attrs -> buildPolicyResponse(attrs.userId().toString(), ALLOW, routeArn))
            .orElseGet(() -> buildPolicyResponse(ANONYMOUS, DENY, routeArn));
    }


    private Map<String, String> collectCookiesByName(final List<String> cookies) {
        return cookies.stream()
            .map(cookie -> cookie.split("="))
            .collect(Collectors.toMap(pair -> pair[0], pair -> pair[1]));
    }

    private IamPolicyResponse buildPolicyResponse(final String principalId, final String effect,
                                                  final String resource) {
        final PolicyDocument.PolicyDocumentBuilder policyBuilder = PolicyDocument.builder();

        if (effect != null && resource != null) {
            final String effectEnum = ALLOW.equalsIgnoreCase(effect) ? ALLOW : DENY;

            final Statement statement = Statement.builder()
                .withEffect(effectEnum)
                .withAction("execute-api:Invoke")
                .withResource(List.of(resource))
                .build();

            policyBuilder.withStatement(List.of(statement));
        }

        final PolicyDocument policyDocument = policyBuilder.build();

        System.out.println("Effect: " + effect);

        return builder()
            .withPrincipalId(principalId)
            .withPolicyDocument(policyDocument)
            .withContext(new HashMap<>())
            .build();
    }
}
